{"version": 3, "file": "auth.js", "sources": ["pages/auth/auth.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYXV0aC9hdXRoLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <view class=\"header\">\r\n      <text class=\"title\">人脸识别认证</text>\r\n    </view>\r\n\r\n    <view class=\"notification\">\r\n      <text>当前业务需要人脸识别验证</text>\r\n    </view>\r\n\r\n    <view class=\"card-icon\">\r\n      <image src=\"/static/face-id-icon.png\" class=\"icon\" />\r\n    </view>\r\n\r\n    <view class=\"form-item\">\r\n      <text class=\"label\">姓名</text>\r\n      <input\r\n        class=\"input-value\"\r\n        :class=\"{ 'input-error': nameError, 'input-disabled': nameDisabled }\"\r\n        placeholder=\"请输入姓名\"\r\n        v-model=\"formData.name\"\r\n        :disabled=\"nameDisabled\"\r\n        @input=\"onNameInput\"\r\n        @blur=\"validateName\"\r\n      />\r\n      <text v-if=\"nameError\" class=\"error-text\">{{ nameError }}</text>\r\n    </view>\r\n\r\n    <view class=\"form-item\">\r\n      <text class=\"label\">身份证号码</text>\r\n      <input\r\n        class=\"input-value\"\r\n        :class=\"{ 'input-error': cardError, 'input-disabled': cardDisabled }\"\r\n        placeholder=\"请输入身份证号码\"\r\n        v-model=\"formData.card\"\r\n        :disabled=\"cardDisabled\"\r\n        @input=\"onCardInput\"\r\n        @blur=\"validateCard\"\r\n        maxlength=\"18\"\r\n      />\r\n      <text v-if=\"cardError\" class=\"error-text\">{{ cardError }}</text>\r\n    </view>\r\n\r\n    <view class=\"footer\">\r\n      <view class=\"agreement\">\r\n        <checkbox-group @change=\"toggleAgreement\">\r\n          <checkbox :checked=\"isAgreed\" color=\"#2979FF\" />\r\n        </checkbox-group>\r\n        <text>我已阅读并同意遵循</text>\r\n        <text class=\"link\" @tap=\"navigateTo('user_agreement')\">《用户服务协议》</text>\r\n        <text>和</text>\r\n        <text class=\"link\" @tap=\"navigateTo('privacy_policy')\">《个人信息保护政策》</text>\r\n      </view>\r\n      <button \r\n        class=\"action-btn\" \r\n        :disabled=\"!canSubmit\" \r\n        :class=\"{ 'disabled': !canSubmit }\"\r\n        @tap=\"startFaceRecognition\"\r\n      >\r\n        {{ isLoading ? '验证中...' : '开始人脸识别验证' }}\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { api } from '@/utils/api.js';\r\nimport wechatAuth from '@/utils/wechat-auth.js';\r\n// HTTP请求拦截器模块\r\nimport { getBaseURL, getTimeout, isDebug } from '@/config/env.js';\r\n\r\n// 表单数据 - 修复phoneForm未定义问题\r\nconst formData = ref({\r\n  name: '',\r\n  card: ''\r\n});\r\n\r\n// 验证状态\r\nconst nameError = ref('');\r\nconst cardError = ref('');\r\nconst isAgreed = ref(false);\r\nconst isLoading = ref(false);\r\n\r\n// 输入框禁用状态 - 用于URL参数预填充时禁用编辑\r\nconst nameDisabled = ref(false);\r\nconst cardDisabled = ref(false);\r\n\r\n// 计算是否可以提交 - 实现按钮状态控制\r\nconst canSubmit = computed(() => {\r\n  return isAgreed.value && \r\n         formData.value.name.trim() && \r\n         formData.value.card.trim() && \r\n         !nameError.value && \r\n         !cardError.value &&\r\n         !isLoading.value;\r\n});\r\n\r\n// 姓名输入处理\r\nconst onNameInput = () => {\r\n  // 清除之前的错误提示\r\n  if (nameError.value) {\r\n    nameError.value = '';\r\n  }\r\n};\r\n\r\n// 姓名验证\r\nconst validateName = () => {\r\n  const name = formData.value.name.trim();\r\n  if (!name) {\r\n    nameError.value = '请输入姓名';\r\n    return false;\r\n  }\r\n  if (name.length < 2) {\r\n    nameError.value = '姓名至少2个字符';\r\n    return false;\r\n  }\r\n  if (name.length > 10) {\r\n    nameError.value = '姓名不能超过10个字符';\r\n    return false;\r\n  }\r\n  // 检查是否包含特殊字符\r\n  if (!/^[\\u4e00-\\u9fa5a-zA-Z]+$/.test(name)) {\r\n    nameError.value = '姓名只能包含中文或英文';\r\n    return false;\r\n  }\r\n  nameError.value = '';\r\n  return true;\r\n};\r\n\r\n// 身份证号输入处理\r\nconst onCardInput = () => {\r\n  // 清除之前的错误提示\r\n  if (cardError.value) {\r\n    cardError.value = '';\r\n  }\r\n  // 转换为大写\r\n  formData.value.card = formData.value.card.toUpperCase();\r\n};\r\n\r\n// 身份证号验证 - 实现格式校验\r\nconst validateCard = () => {\r\n  const card = formData.value.card.trim();\r\n  if (!card) {\r\n    cardError.value = '请输入身份证号码';\r\n    return false;\r\n  }\r\n  \r\n  // 身份证号格式验证（18位，最后一位可为X）\r\n  const idCardRegex = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\r\n  \r\n  if (!idCardRegex.test(card)) {\r\n    cardError.value = '身份证号码格式不正确';\r\n    return false;\r\n  }\r\n  \r\n  // 身份证校验码验证\r\n  if (!validateIdCardChecksum(card)) {\r\n    cardError.value = '身份证号码校验失败';\r\n    return false;\r\n  }\r\n  \r\n  cardError.value = '';\r\n  return true;\r\n};\r\n\r\n// 身份证校验码验证\r\nconst validateIdCardChecksum = (idCard) => {\r\n  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];\r\n  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];\r\n  \r\n  let sum = 0;\r\n  for (let i = 0; i < 17; i++) {\r\n    sum += parseInt(idCard[i]) * weights[i];\r\n  }\r\n  \r\n  const checkCode = checkCodes[sum % 11];\r\n  return checkCode === idCard[17];\r\n};\r\n\r\n// 协议同意状态切换\r\nconst toggleAgreement = () => {\r\n  isAgreed.value = !isAgreed.value;\r\n};\r\n\r\n// 页面导航\r\nconst navigateTo = (page) => {\r\n  uni.navigateTo({\r\n    url: `/pages/${page}/${page}`\r\n  });\r\n};\r\n\r\n// 记录操作日志\r\n/* const recordOperation = async (buttonName, details = {}) => {\r\n  try {\r\n    const pages = getCurrentPages();\r\n    const currentPage = pages[pages.length - 1];\r\n    \r\n    await api.operationLog.recordOperation({\r\n      menu_name: '人脸识别认证',\r\n      button_name: buttonName,\r\n      browser_path: `/${currentPage.route}`,\r\n      operation_details: JSON.stringify({\r\n        ...details,\r\n        timestamp: new Date().toISOString()\r\n      })\r\n    });\r\n  } catch (error) {\r\n    console.warn('记录操作日志失败:', error);\r\n  }\r\n}; */\r\n\r\n// 获取用户openid\r\nconst getUserOpenid = () => {\r\n  try {\r\n    // 优先从wechatAuth获取\r\n    if (wechatAuth.openid) {\r\n      return wechatAuth.openid;\r\n    }\r\n    \r\n    // 从存储中获取\r\n    const openid = uni.getStorageSync('openid');\r\n    if (openid) {\r\n      return openid;\r\n    }\r\n    \r\n    throw new Error('未获取到用户openid');\r\n  } catch (error) {\r\n    console.error('获取openid失败:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// 开始人脸识别验证 - 实现完整的接口调用逻辑\r\nconst startFaceRecognition = async () => {\r\n  // 记录按钮点击操作\r\n  /* await recordOperation('开始人脸识别验证', {\r\n    name: formData.value.name,\r\n    hasIdCard: !!formData.value.card\r\n  }); */\r\n\r\n  // 最终表单验证\r\n  if (!validateName() || !validateCard()) {\r\n    uni.showToast({\r\n      title: '请填写正确的信息',\r\n      icon: 'none',\r\n      duration: 2000\r\n    });\r\n    return;\r\n  }\r\n\r\n  isLoading.value = true;\r\n\r\n  try {\r\n    // 获取用户openid\r\n    const openid = getUserOpenid();\r\n\r\n    // 调用人脸核身接口 - 使用统一的API管理\r\n    const response = await api.wechat.faceIdAuth({\r\n      openid: openid,\r\n      name: formData.value.name.trim(),\r\n      id_card: formData.value.card.trim()\r\n    });\r\n\r\n    // API统一管理已处理HTTP状态码，这里直接处理业务数据\r\n    console.log('人脸核身接口响应:', response);\r\n    // 检查响应是否包含验证URL\r\n    if (response.data) {\r\n        // 记录成功操作\r\n        /* await recordOperation('人脸识别验证_跳转成功', {\r\n          hasUrl: true\r\n        }); */\r\n        \r\n        // 跳转到腾讯云人脸核身页面responseData = https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx1f7125112b74db52&redirect_uri=https%3A%2F%2Fopen.faceid.qq.com%2Fv1%2Fapi%2FgetCode%3FbizRedirect%3Dhttps%253A%252F%252Ffaceid.qq.com%252Fapi%252Fauth%252FgetOpenidAndSaveToken%253Ftoken%253DF0F626E2-285F-470A-A6E6-4780FBE8D098&response_type=code&scope=snsapi_base&state=&component_appid=wx9802ee81e68d6dee#wechat_redirect\r\n        /* uni.redirectTo({\r\n          url: responseData.data\r\n        }); */\r\n        uni.navigateTo({\r\n          url: `/pages/webview/webview?url=${encodeURIComponent(response.data)}`\r\n        });\r\n      } else {\r\n        throw new Error(response.msg || '获取验证地址失败');\r\n      }\r\n  } catch (error) {\r\n    console.error('人脸识别验证失败:', error);\r\n    \r\n    // 记录失败操作\r\n    /* await recordOperation('人脸识别验证_失败', {\r\n      error: error.msg\r\n    }); */\r\n    \r\n    // 显示用户友好的错误提示\r\n    let errorMessage = '验证失败，请重试';\r\n    \r\n    /* if (error.message.includes('openid')) {\r\n      errorMessage = '用户身份验证失败，请重新登录';\r\n    } else if (error.message.includes('网络')) {\r\n      errorMessage = '网络连接失败，请检查网络设置';\r\n    } else if (error.message.includes('超时')) {\r\n      errorMessage = '请求超时，请重试';\r\n    } */\r\n    \r\n    uni.showToast({\r\n      title: errorMessage,\r\n      icon: 'none',\r\n      duration: 3000\r\n    });\r\n  } finally {\r\n    isLoading.value = false;\r\n  }\r\n};\r\n\r\n/* // 获取环境配置中的baseURL\r\nconst getBaseURL = () => {\r\n  try {\r\n    const { getBaseURL } = require('@/config/env.js');\r\n    return getBaseURL();\r\n  } catch (error) {\r\n    console.warn('获取baseURL失败，使用默认值');\r\n    return 'https://your-backend-api.com';\r\n  }\r\n}; */\r\n\r\n// 解析URL参数并填充表单\r\nconst parseUrlParams = (options = {}) => {\r\n  try {\r\n    console.log('开始解析URL参数...');\r\n    console.log('接收到的页面参数:', options);\r\n\r\n    // 处理name参数\r\n    if (options.name) {\r\n      // URL解码\r\n      const decodedName = decodeURIComponent(options.name);\r\n      console.log('解析到name参数:', decodedName);\r\n\r\n      // 填充到表单\r\n      formData.value.name = decodedName;\r\n      // 设置为禁用状态\r\n      nameDisabled.value = true;\r\n\r\n      // 自动验证姓名\r\n      validateName();\r\n    }\r\n\r\n    // 处理idCard参数\r\n    if (options.idCard) {\r\n      console.log('解析到idCard参数:', options.idCard);\r\n\r\n      // 填充到表单\r\n      formData.value.card = options.idCard;\r\n      // 设置为禁用状态\r\n      cardDisabled.value = true;\r\n\r\n      // 自动验证身份证号\r\n      validateCard();\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('解析URL参数失败:', error);\r\n  }\r\n};\r\n\r\n// 页面挂载时的初始化\r\nonMounted(async () => {\r\n  // 获取页面参数并解析\r\n  try {\r\n    const pages = uni.getCurrentPages();\r\n    const currentPage = pages[pages.length - 1];\r\n    const options = currentPage.options || {};\r\n\r\n    console.log('页面挂载，获取参数:', options);\r\n    // 解析URL参数并填充表单\r\n    parseUrlParams(options);\r\n  } catch (error) {\r\n    console.error('获取页面参数失败:', error);\r\n  }\r\n\r\n  /* try {\r\n    // 记录页面访问\r\n    await recordOperation('页面访问', {\r\n      pageName: '人脸识别认证'\r\n    });\r\n  } catch (error) {\r\n    console.warn('记录页面访问失败:', error);\r\n  } */\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #ffffff;\r\n  min-height: 100vh;\r\n  padding: 20px;\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 40rpx;\r\n}\r\n\r\n.title {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  color: #333333;\r\n}\r\n\r\n.notification {\r\n  text-align: center;\r\n  margin-top: 20rpx;\r\n  color: #666666;\r\n  font-size: 14px;\r\n}\r\n\r\n.card-icon {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin: 40rpx 0;\r\n}\r\n\r\n.icon {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  background-color: #2979FF;\r\n  border-radius: 16rpx;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.label {\r\n  font-size: 16px;\r\n  color: #333333;\r\n  margin-bottom: 10rpx;\r\n  display: block;\r\n}\r\n\r\n.input-value {\r\n  font-size: 16px;\r\n  color: #333333;\r\n  border-bottom: 1px solid #eeeeee;\r\n  padding-bottom: 15rpx;\r\n  width: 100%;\r\n}\r\n\r\n.input-error {\r\n  border-bottom-color: #ff4757 !important;\r\n}\r\n\r\n.input-disabled {\r\n  background-color: #f5f5f5 !important;\r\n  color: #999999 !important;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.error-text {\r\n  color: #ff4757;\r\n  font-size: 12px;\r\n  margin-top: 5rpx;\r\n  display: block;\r\n}\r\n\r\n.footer {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 30rpx;\r\n  background-color: #ffffff;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.agreement {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-bottom: 30rpx;\r\n  font-size: 14px;\r\n  color: #666666;\r\n}\r\n\r\n.link {\r\n  color: #2979FF;\r\n  margin: 0 5rpx;\r\n}\r\n\r\n.action-btn {\r\n  background-color: #2979FF;\r\n  color: #ffffff;\r\n  border-radius: 50rpx;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.disabled {\r\n  background-color: #CCCCCC !important;\r\n  opacity: 0.7;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/auth/auth.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "wechatAuth", "api", "onMounted"], "mappings": ";;;;;;;;;AAyEA,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAGD,UAAM,YAAYA,cAAAA,IAAI,EAAE;AACxB,UAAM,YAAYA,cAAAA,IAAI,EAAE;AACxB,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAC1B,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAG3B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAG9B,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAC/B,aAAO,SAAS,SACT,SAAS,MAAM,KAAK,KAAM,KAC1B,SAAS,MAAM,KAAK,KAAM,KAC1B,CAAC,UAAU,SACX,CAAC,UAAU,SACX,CAAC,UAAU;AAAA,IACpB,CAAC;AAGD,UAAM,cAAc,MAAM;AAExB,UAAI,UAAU,OAAO;AACnB,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,YAAM,OAAO,SAAS,MAAM,KAAK,KAAI;AACrC,UAAI,CAAC,MAAM;AACT,kBAAU,QAAQ;AAClB,eAAO;AAAA,MACR;AACD,UAAI,KAAK,SAAS,GAAG;AACnB,kBAAU,QAAQ;AAClB,eAAO;AAAA,MACR;AACD,UAAI,KAAK,SAAS,IAAI;AACpB,kBAAU,QAAQ;AAClB,eAAO;AAAA,MACR;AAED,UAAI,CAAC,2BAA2B,KAAK,IAAI,GAAG;AAC1C,kBAAU,QAAQ;AAClB,eAAO;AAAA,MACR;AACD,gBAAU,QAAQ;AAClB,aAAO;AAAA,IACT;AAGA,UAAM,cAAc,MAAM;AAExB,UAAI,UAAU,OAAO;AACnB,kBAAU,QAAQ;AAAA,MACnB;AAED,eAAS,MAAM,OAAO,SAAS,MAAM,KAAK;IAC5C;AAGA,UAAM,eAAe,MAAM;AACzB,YAAM,OAAO,SAAS,MAAM,KAAK,KAAI;AACrC,UAAI,CAAC,MAAM;AACT,kBAAU,QAAQ;AAClB,eAAO;AAAA,MACR;AAGD,YAAM,cAAc;AAEpB,UAAI,CAAC,YAAY,KAAK,IAAI,GAAG;AAC3B,kBAAU,QAAQ;AAClB,eAAO;AAAA,MACR;AAGD,UAAI,CAAC,uBAAuB,IAAI,GAAG;AACjC,kBAAU,QAAQ;AAClB,eAAO;AAAA,MACR;AAED,gBAAU,QAAQ;AAClB,aAAO;AAAA,IACT;AAGA,UAAM,yBAAyB,CAAC,WAAW;AACzC,YAAM,UAAU,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;AACpE,YAAM,aAAa,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAEzE,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,eAAO,SAAS,OAAO,CAAC,CAAC,IAAI,QAAQ,CAAC;AAAA,MACvC;AAED,YAAM,YAAY,WAAW,MAAM,EAAE;AACrC,aAAO,cAAc,OAAO,EAAE;AAAA,IAChC;AAGA,UAAM,kBAAkB,MAAM;AAC5B,eAAS,QAAQ,CAAC,SAAS;AAAA,IAC7B;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3BC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,UAAU,IAAI,IAAI,IAAI;AAAA,MAC/B,CAAG;AAAA,IACH;AAuBA,UAAM,gBAAgB,MAAM;AAC1B,UAAI;AAEF,YAAIC,iBAAAA,WAAW,QAAQ;AACrB,iBAAOA,iBAAAA,WAAW;AAAA,QACnB;AAGD,cAAM,SAASD,cAAAA,MAAI,eAAe,QAAQ;AAC1C,YAAI,QAAQ;AACV,iBAAO;AAAA,QACR;AAED,cAAM,IAAI,MAAM,cAAc;AAAA,MAC/B,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,8BAAA,eAAe,KAAK;AAClC,cAAM;AAAA,MACP;AAAA,IACH;AAGA,UAAM,uBAAuB,YAAY;AAQvC,UAAI,CAAC,aAAY,KAAM,CAAC,gBAAgB;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AAED,gBAAU,QAAQ;AAElB,UAAI;AAEF,cAAM,SAAS;AAGf,cAAM,WAAW,MAAME,cAAI,OAAO,WAAW;AAAA,UAC3C;AAAA,UACA,MAAM,SAAS,MAAM,KAAK,KAAM;AAAA,UAChC,SAAS,SAAS,MAAM,KAAK,KAAM;AAAA,QACzC,CAAK;AAGDF,sBAAY,MAAA,MAAA,OAAA,8BAAA,aAAa,QAAQ;AAEjC,YAAI,SAAS,MAAM;AAUfA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,8BAA8B,mBAAmB,SAAS,IAAI,CAAC;AAAA,UAC9E,CAAS;AAAA,QACT,OAAa;AACL,gBAAM,IAAI,MAAM,SAAS,OAAO,UAAU;AAAA,QAC3C;AAAA,MACJ,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,KAAK;AAQhC,YAAI,eAAe;AAUnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACL,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAcA,UAAM,iBAAiB,CAAC,UAAU,OAAO;AACvC,UAAI;AACFA,sBAAAA,MAAA,MAAA,OAAA,8BAAY,cAAc;AAC1BA,sBAAY,MAAA,MAAA,OAAA,8BAAA,aAAa,OAAO;AAGhC,YAAI,QAAQ,MAAM;AAEhB,gBAAM,cAAc,mBAAmB,QAAQ,IAAI;AACnDA,wBAAY,MAAA,MAAA,OAAA,8BAAA,cAAc,WAAW;AAGrC,mBAAS,MAAM,OAAO;AAEtB,uBAAa,QAAQ;AAGrB;QACD;AAGD,YAAI,QAAQ,QAAQ;AAClBA,yEAAY,gBAAgB,QAAQ,MAAM;AAG1C,mBAAS,MAAM,OAAO,QAAQ;AAE9B,uBAAa,QAAQ;AAGrB;QACD;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,8BAAA,cAAc,KAAK;AAAA,MAClC;AAAA,IACH;AAGAG,kBAAAA,UAAU,YAAY;AAEpB,UAAI;AACF,cAAM,QAAQH,oBAAI;AAClB,cAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,cAAM,UAAU,YAAY,WAAW;AAEvCA,sBAAY,MAAA,MAAA,OAAA,8BAAA,cAAc,OAAO;AAEjC,uBAAe,OAAO;AAAA,MACvB,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,KAAK;AAAA,MACjC;AAAA,IAUH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChYD,GAAG,WAAW,eAAe;"}