{"version": 3, "file": "mediation_query.js", "sources": ["pages/mediation_query/mediation_query.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWVkaWF0aW9uX3F1ZXJ5L21lZGlhdGlvbl9xdWVyeS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"mediation-query-container\">\r\n    <!-- 未认证状态 - 身份信息查询 -->\r\n    <view class=\"auth-required\" v-if=\"!isAuthenticated\">\r\n      <view class=\"form-container\">\r\n        <view class=\"form-card\">\r\n          <view class=\"card-header\">\r\n            <text class=\"card-title\">姓名</text>\r\n          </view>\r\n          <view class=\"card-content\">\r\n            <uni-easyinput\r\n              v-model=\"formData.name\"\r\n              placeholder=\"请输入真实姓名\"\r\n              class=\"input-field\"\r\n            >\r\n            </uni-easyinput>\r\n          </view>\r\n        </view>\r\n        <view class=\"form-card\">\r\n          <view class=\"card-header\">\r\n            <text class=\"card-title\">身份证号</text>\r\n          </view>\r\n          <view class=\"card-content\">\r\n            <uni-easyinput\r\n              v-model=\"formData.card\"\r\n              placeholder=\"请输入身份证号\"\r\n              class=\"input-field\"\r\n            >\r\n            </uni-easyinput>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view class=\"help-text\">\r\n        <i class=\"fas fa-info-circle\"></i>仅显示案件数量统计\r\n      </view>\r\n      <button class=\"auth-button\" @click=\"handleSearch\">查询案件</button>\r\n      \r\n      <!-- 身份信息查询结果显示 -->\r\n      <view v-if=\"mediationCase !== '' && (formData.name || formData.card)\">\r\n        <view class=\"query-result-card\" @click=\"navigateToAuth\">\r\n          <view class=\"query-result-icon\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </view>\r\n          <view class=\"query-result-text\">{{mediationCase}}</view>\r\n        </view>\r\n        <view class=\"query-tip\">\r\n          <view>需要查看详细信息？<text href=\"#\" class=\"certification\" @click=\"navigateToAuth\">去认证</text></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <!-- 已认证状态 - 案件列表 -->\r\n    <view v-if=\"isAuthenticated\">\r\n      <!-- 搜索框 -->\r\n      <view class=\"search-box\">\r\n        <uni-easyinput\r\n          class=\"search-input-wrapper\"\r\n          v-model=\"searchKeyword\"\r\n          placeholder=\"请输入调解案件号进行查询\"\r\n          confirmType=\"search\"\r\n          suffixIcon=\"search\"\r\n          :clearable=\"true\"\r\n          @confirm=\"handleSearch\"\r\n          @iconClick=\"handleSearch\"\r\n        ></uni-easyinput>\r\n      </view>\r\n\r\n      <!-- 工单列表 -->\r\n      <view class=\"order-list\" v-if=\"filteredOrderList.length > 0\">\r\n        <view\r\n          class=\"order-item\"\r\n          v-for=\"order in filteredOrderList\"\r\n          :key=\"order.id\"\r\n          @click=\"navigateToDetail(order.id,order.status)\"\r\n          hover-class=\"order-item-hover\"\r\n          :class=\"{ 'order-item-animation': isLoadingAnimate }\"\r\n        >\r\n          <view class=\"order-info\">\r\n            <view class=\"order-id\"\r\n              >调解案件号: <text class=\"id-text\">{{ order.id }}</text></view\r\n            >\r\n            <view\r\n              class=\"status-label\"\r\n              :style=\"{\r\n                backgroundColor:\r\n                  statusStyles[order.statusCode]?.bgColor || '#999',\r\n                color: '#fff',\r\n              }\"\r\n              >{{ order.status }}</view>\r\n          </view>\r\n          <view class=\"order-date-icon\">\r\n            <view class=\"order-date\">发起日期: {{ order.createDate }}</view>\r\n            <view class=\"arrow-icon\">›</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 加载中状态 -->\r\n      <view class=\"loading-state\" v-if=\"isLoading && !isRefreshing\">\r\n        <view class=\"loading-spinner\"></view>\r\n        <text class=\"loading-text\">加载中...</text>\r\n      </view>\r\n\r\n      <!-- 空状态 -->\r\n      <view class=\"empty-state\" v-if=\"filteredOrderList.length === 0 && !isLoading\">\r\n        <image\r\n          class=\"empty-image\"\r\n          src=\"/static/icons/empty.png\"\r\n          mode=\"aspectFit\"\r\n        ></image>\r\n        <text class=\"empty-text\">{{\r\n          searchKeyword ? \"未找到相关调解案件号\" : \"暂无信息\"\r\n        }}</text>\r\n        <button class=\"refresh-button\" @click=\"handleRefresh\">刷新</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from \"vue\";\r\nimport { onPullDownRefresh } from \"@dcloudio/uni-app\";\r\nimport { api } from \"@/utils/api.js\";\r\n\r\n// 搜索关键词\r\nconst searchKeyword = ref(\"\");\r\n// 是否正在刷新\r\nconst isRefreshing = ref(false);\r\n// 是否正在加载\r\nconst isLoading = ref(false);\r\n// 加载动画效果\r\nconst isLoadingAnimate = ref(false);\r\n// 用户认证状态\r\nconst isAuthenticated = ref(false);\r\n// 调解案件数量\r\nconst mediationCase = ref('');\r\n// 工单列表\r\nconst orderList = ref([]);\r\n\r\n// 表单数据\r\nconst formData = reactive({\r\n  name: '',           // 姓名\r\n  card: ''            // 身份证号\r\n});\r\n\r\n// 状态样式配置\r\nconst statusStyles = {\r\n  pending: { bgColor: \"#faad14\" },\r\n  processing: { bgColor: \"#1890ff\" },\r\n  completed: { bgColor: \"#52c41a\" },\r\n  closed: { bgColor: \"#999\" },\r\n};\r\n\r\n// 根据搜索关键词过滤的工单列表\r\nconst filteredOrderList = computed(() => {\r\n  if (!searchKeyword.value) {\r\n    return orderList.value;\r\n  }\r\n  const keyword = searchKeyword.value.toLowerCase();\r\n  return orderList.value.filter((order) =>\r\n    order.id.toLowerCase().includes(keyword)\r\n  );\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  console.log(\"调解查询页面已加载\");\r\n  checkAuthStatus();\r\n  setTimeout(() => {\r\n    isLoadingAnimate.value = true;\r\n  }, 200);\r\n});\r\n\r\n// 检查用户认证状态\r\nconst checkAuthStatus = () => {\r\n  const userIsStaff = uni.getStorageSync(\"user_is_staff\");\r\n  isAuthenticated.value = userIsStaff === true;\r\n  \r\n  console.log(\"用户认证状态:\", isAuthenticated.value ? \"已认证\" : \"未认证\");\r\n  \r\n  // 如果已认证，加载案件列表\r\n  if (isAuthenticated.value) {\r\n    fetchOrderList();\r\n  }\r\n};\r\n\r\n// 下拉刷新处理\r\nonPullDownRefresh(() => {\r\n  console.log(\"触发下拉刷新\");\r\n  isRefreshing.value = true;\r\n  checkAuthStatus();\r\n});\r\n\r\n// 获取工单列表 - 已认证用户\r\nconst fetchOrderList = async (caseNumber = '') => {\r\n  isLoading.value = true;\r\n  \r\n  if (!isRefreshing.value) {\r\n    uni.showLoading({ title: \"加载中...\" });\r\n  }\r\n\r\n  try {\r\n    const params = {};\r\n    if (caseNumber) {\r\n      params.mediation_case_number = caseNumber;\r\n    }\r\n\r\n    const result = await api.mediationQuery.getAuthenticatedList(params);\r\n    \r\n    if (!isRefreshing.value) {\r\n      uni.hideLoading();\r\n    }\r\n\r\n    if (result && result.success && result.data) {\r\n      orderList.value = result.data.map(item => ({\r\n        id: item.case_number || item.id,\r\n        status: item.case_status_cn || '未知状态',\r\n        statusCode: getStatusCode(item.case_status_cn),\r\n        createDate: formatDate(item.initiate_date),\r\n        rawData: item\r\n      }));\r\n    } else {\r\n      orderList.value = [];\r\n    }\r\n\r\n    isLoading.value = false;\r\n\r\n    if (isRefreshing.value) {\r\n      uni.stopPullDownRefresh();\r\n      isRefreshing.value = false;\r\n      uni.showToast({\r\n        title: \"刷新成功\",\r\n        icon: \"success\",\r\n        duration: 1500,\r\n      });\r\n    }\r\n\r\n  } catch (error) {\r\n    if (!isRefreshing.value) {\r\n      uni.hideLoading();\r\n    }\r\n    \r\n    console.error(\"获取案件列表失败:\", error);\r\n    orderList.value = [];\r\n    isLoading.value = false;\r\n\r\n    if (isRefreshing.value) {\r\n      uni.stopPullDownRefresh();\r\n      isRefreshing.value = false;\r\n    }\r\n\r\n    uni.showToast({\r\n      title: \"获取数据失败\",\r\n      icon: \"none\",\r\n      duration: 2000,\r\n    });\r\n  }\r\n};\r\n\r\n// 处理搜索\r\nconst handleSearch = async () => {\r\n  if (isAuthenticated.value) {\r\n    // 已认证用户 - 搜索案件列表\r\n    await fetchOrderList(searchKeyword.value.trim());\r\n  } else {\r\n    // 未认证用户 - 身份信息查询\r\n    await handleIdentitySearch();\r\n  }\r\n};\r\n\r\n// 处理身份信息查询 - 未认证用户\r\nconst handleIdentitySearch = async () => {\r\n  const name = formData.name.trim();\r\n  const idCard = formData.card.trim();\r\n\r\n  if (!name || !idCard) {\r\n    uni.showToast({\r\n      title: \"请输入完整的姓名和身份证号\",\r\n      icon: \"none\",\r\n      duration: 1500,\r\n    });\r\n    return;\r\n  }\r\n\r\n  uni.showLoading({ title: \"查询中...\" });\r\n  \r\n  const result = await api.mediationQuery.getCaseCountByIdentity({\r\n    name: name,\r\n    id_card: idCard\r\n  });\r\n\r\n  uni.hideLoading();\r\n\r\n  console.log(result.data,'=====未认证',result)\r\n  if (result.state == \"success\") {\r\n    const count = result.data;\r\n    mediationCase.value = count;\r\n  } else {\r\n    mediationCase.value = null;\r\n    uni.showToast({\r\n      title: result.msg,\r\n      icon: \"none\",\r\n      duration: 2000,\r\n    });\r\n  }\r\n};\r\n\r\n// 跳转到认证页面 - 未认证用户点击查询结果\r\nconst navigateToAuth = () => {\r\n  const name = formData.name.trim();\r\n  const idCard = formData.card.trim();\r\n  \r\n  uni.navigateTo({\r\n    url: `/pages/auth/auth?name=${encodeURIComponent(name)}&idCard=${encodeURIComponent(idCard)}`,\r\n    success: () => {\r\n      console.log(\"跳转到认证页面\");\r\n    },\r\n    fail: (err) => {\r\n      console.error(\"跳转失败\", err);\r\n      uni.showToast({\r\n        title: \"跳转失败\",\r\n        icon: \"none\",\r\n      });\r\n    },\r\n  });\r\n};\r\n\r\n// 手动刷新\r\nconst handleRefresh = () => {\r\n  if (isAuthenticated.value) {\r\n    fetchOrderList();\r\n  }\r\n};\r\n\r\n// 工具函数：根据案件状态中文获取状态码\r\nconst getStatusCode = (statusCn) => {\r\n  const statusMap = {\r\n    '待确认': 'pending',\r\n    '进行中': 'processing', \r\n    '已完成': 'completed',\r\n    '已关闭': 'closed',\r\n    '待处理': 'pending',\r\n    '处理中': 'processing',\r\n    '已结案': 'completed',\r\n    '已撤销': 'closed'\r\n  };\r\n  return statusMap[statusCn] || 'pending';\r\n};\r\n\r\n// 工具函数：格式化日期\r\nconst formatDate = (dateStr) => {\r\n  if (!dateStr) return '';\r\n  const date = new Date(dateStr);\r\n  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\r\n};\r\n\r\n// 跳转到工单详情页 - 已认证用户\r\nconst navigateToDetail = (orderId, orderStatus) => {\r\n  console.log(orderId, '=====orderStatus', orderStatus);\r\n  \r\n  if (orderStatus === \"待确认\") {\r\n    uni.navigateTo({\r\n      url: `/pages/work_order_detail/work_order_detail?id=${orderId}&status=${orderStatus}`,\r\n    });\r\n  } else if (orderStatus === \"进行中\") {\r\n    uni.navigateTo({\r\n      url: `/pages/solution_confirm/solution_confirm?orderId=${orderId}&status=${orderStatus}`,\r\n    });\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:root {\r\n\t--primary-color: #3b7eeb;      // 主题蓝色\r\n\t--primary-light: #e6f0ff;      // 浅蓝色背景\r\n\t--success-color: #52c41a;\r\n\t--text-color:#333;\r\n}\r\n.mediation-query-container {\r\n  min-height: 100vh;\r\n  // height: calc(100% - 94px);\r\n  overflow-y: auto;\r\n  background-color: rgb(248, 250, 252);\r\n  padding: 30rpx 30rpx 140rpx;\r\n}\r\n\r\n.search-box {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.search-input-wrapper {\r\n  flex: 1;\r\n}\r\n\r\n/* 调整uni-easyinput的样式 */\r\n:deep(.uni-easyinput__content) {\r\n  height: 80rpx;\r\n  background-color: #fff;\r\n  border: 2rpx solid #eee;\r\n  border-radius: 40rpx;\r\n  padding: 0 20rpx;\r\n}\r\n\r\n:deep(.uni-icons) {\r\n  color: #999;\r\n}\r\n\r\n:deep(.uni-easyinput__placeholder-class) {\r\n  font-size: 28rpx;\r\n}\r\n\r\n:deep(.uni-easyinput__content-input) {\r\n  height: 80rpx;\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* 未认证状态提示样式 */\r\n.auth-required {\r\n  // flex-direction: column;\r\n  // align-items: center;\r\n  // justify-content: center;\r\n  // padding: 100rpx 50rpx;\r\n  .filter-tabs {\r\n    display: flex;\r\n    text-align: center;\r\n    // gap: 20rpx;\r\n    overflow-x: auto;\r\n\r\n    .type-card {\r\n      // padding: 15rpx 30rpx;\r\n      // background-color: #f8f8f8;\r\n      // color: #666;\r\n      // border-radius: 30rpx;\r\n      // font-size: 26rpx;\r\n      // white-space: nowrap;\r\n      // transition: all 0.3s ease;\r\n\r\n      background-color: #fff;\r\n      color: #666;\r\n      position: relative;\r\n      flex: 1 1 0%;\r\n      border-width: 2rpx;\r\n      border-style: solid;\r\n      border-color: #fff;\r\n      box-shadow: rgba(0, 0, 0, 0.1) 0 4rpx 8rpx;\r\n      border-image: initial;\r\n      font-size: 28rpx;\r\n      padding: 16rpx 32rpx;\r\n      border-radius: 12rpx;\r\n    }\r\n\r\n    .type-card.active {\r\n      background-color: #2979ff;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n\t--card-background: #ffffff;    // 卡片背景色\r\n\r\n\t.form-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\t.form-card {\r\n\t\t\toverflow: hidden;\r\n\t\t\t.card-header {\r\n\t\t\tpadding: 30rpx 30rpx 20rpx 10rpx;\r\n\t\t\t// border-bottom: 1rpx solid #f0f0f0;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.fas {\r\n\t\t\t\tmargin-right: 8px;\r\n\t\t\t\tcolor: var(--primary-color);\r\n\t\t\t}\r\n\r\n\t\t\t.card-title {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: var(--text-primary);\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t\t// flex: 1;\r\n\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.query-result-card {\r\n\t\tbackground: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);\r\n\t\tborder: 2rpx solid #b7eb8f;\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 40rpx;\r\n\t\tmargin-top: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.1);\r\n\t\tanimation: slideInUp 0.3s ease;\r\n\t}\r\n  .query-tip{\r\n    text-align: center; margin-top: 20px; font-size: 13px; color: var(--text-secondary);\r\n  }\r\n  .certification{\r\n    color: var(--primary-color); text-decoration: none;\r\n  }\r\n\t.query-result-icon {\r\n\t\twidth: 96rpx;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: linear-gradient(135deg, var(--success-color) 0%, #45a049 100%);\r\n\t\tdisplay: flex\t;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin: 0 auto 32rpx;\r\n\t\tcolor: white;\r\n\t\tfont-size: 40rpx;\r\n\t}\r\n\t.query-result-text {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: var(--text-color);\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t.help-text{\r\n\t\tbackground-color: var(--primary-light);\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: var(--primary-color);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tborder-width: 2rpx;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: rgba(59, 126, 235, 0.2);\r\n\t\tborder-image: initial;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 24rpx 32rpx;\r\n\t\tmargin: 32rpx 0;\r\n\t\tgap: 16rpx;\r\n\t\t.fas {\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t\tpadding-top: 4rpx;\r\n\t\t}\r\n\t}\r\n\t.auth-button {\r\n\t\twidth: 690rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tbackground-color: #2979ff;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n}\r\n:deep(.uni-easyinput__content) {\r\n\tborder-radius: 12rpx !important;\r\n}\r\n.order-list {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.order-item {\r\n  margin-bottom: 20rpx;\r\n  padding: 30rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\r\n  display: block;\r\n  unicode-bidi: isolate\r\n}\r\n\r\n.order-item-hover {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.order-item-animation {\r\n  animation: fadeIn 0.5s ease-out forwards;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20rpx);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.order-info {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.order-id {\r\n  font-size: 32rpx;\r\n  color: #333;\r\n  font-weight: 500;\r\n}\r\n\r\n.order-date {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n.order-date-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.status-label {\r\n  padding: 6rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  font-size: 24rpx;\r\n  white-space: nowrap;\r\n}\r\n\r\n.arrow-icon {\r\n  margin-left: 10rpx;\r\n  font-size: 50rpx;\r\n  color: #666;\r\n}\r\n\r\n.loading-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 100rpx;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border: 6rpx solid #f3f3f3;\r\n  border-top: 6rpx solid #2979ff;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.loading-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 100rpx;\r\n}\r\n\r\n.empty-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.refresh-button {\r\n  width: 200rpx;\r\n  height: 70rpx;\r\n  line-height: 70rpx;\r\n  font-size: 28rpx;\r\n  color: #2979ff;\r\n  background-color: #fff;\r\n  border: 2rpx solid #2979ff;\r\n  border-radius: 35rpx;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/mediation_query/mediation_query.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "onMounted", "uni", "onPullDownRefresh", "api"], "mappings": ";;;;;;;;;;;;;;;AA4HA,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAE5B,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAE9B,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAE3B,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAElC,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AAEjC,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAE5B,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AAGxB,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,MAAM;AAAA;AAAA,MACN,MAAM;AAAA;AAAA,IACR,CAAC;AAGD,UAAM,eAAe;AAAA,MACnB,SAAS,EAAE,SAAS,UAAW;AAAA,MAC/B,YAAY,EAAE,SAAS,UAAW;AAAA,MAClC,WAAW,EAAE,SAAS,UAAW;AAAA,MACjC,QAAQ,EAAE,SAAS,OAAQ;AAAA,IAC7B;AAGA,UAAM,oBAAoBC,cAAQ,SAAC,MAAM;AACvC,UAAI,CAAC,cAAc,OAAO;AACxB,eAAO,UAAU;AAAA,MAClB;AACD,YAAM,UAAU,cAAc,MAAM,YAAW;AAC/C,aAAO,UAAU,MAAM;AAAA,QAAO,CAAC,UAC7B,MAAM,GAAG,cAAc,SAAS,OAAO;AAAA,MAC3C;AAAA,IACA,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACdC,oBAAAA,MAAA,MAAA,OAAA,oDAAY,WAAW;AACvB;AACA,iBAAW,MAAM;AACf,yBAAiB,QAAQ;AAAA,MAC1B,GAAE,GAAG;AAAA,IACR,CAAC;AAGD,UAAM,kBAAkB,MAAM;AAC5B,YAAM,cAAcA,cAAAA,MAAI,eAAe,eAAe;AACtD,sBAAgB,QAAQ,gBAAgB;AAExCA,0BAAY,MAAA,OAAA,oDAAA,WAAW,gBAAgB,QAAQ,QAAQ,KAAK;AAG5D,UAAI,gBAAgB,OAAO;AACzB;MACD;AAAA,IACH;AAGAC,kBAAAA,kBAAkB,MAAM;AACtBD,oBAAAA,MAAA,MAAA,OAAA,oDAAY,QAAQ;AACpB,mBAAa,QAAQ;AACrB;IACF,CAAC;AAGD,UAAM,iBAAiB,OAAO,aAAa,OAAO;AAChD,gBAAU,QAAQ;AAElB,UAAI,CAAC,aAAa,OAAO;AACvBA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAAA,MACpC;AAED,UAAI;AACF,cAAM,SAAS,CAAA;AACf,YAAI,YAAY;AACd,iBAAO,wBAAwB;AAAA,QAChC;AAED,cAAM,SAAS,MAAME,UAAG,IAAC,eAAe,qBAAqB,MAAM;AAEnE,YAAI,CAAC,aAAa,OAAO;AACvBF,wBAAG,MAAC,YAAW;AAAA,QAChB;AAED,YAAI,UAAU,OAAO,WAAW,OAAO,MAAM;AAC3C,oBAAU,QAAQ,OAAO,KAAK,IAAI,WAAS;AAAA,YACzC,IAAI,KAAK,eAAe,KAAK;AAAA,YAC7B,QAAQ,KAAK,kBAAkB;AAAA,YAC/B,YAAY,cAAc,KAAK,cAAc;AAAA,YAC7C,YAAY,WAAW,KAAK,aAAa;AAAA,YACzC,SAAS;AAAA,UACV,EAAC;AAAA,QACR,OAAW;AACL,oBAAU,QAAQ;QACnB;AAED,kBAAU,QAAQ;AAElB,YAAI,aAAa,OAAO;AACtBA,wBAAG,MAAC,oBAAmB;AACvB,uBAAa,QAAQ;AACrBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MAEF,SAAQ,OAAO;AACd,YAAI,CAAC,aAAa,OAAO;AACvBA,wBAAG,MAAC,YAAW;AAAA,QAChB;AAEDA,sBAAc,MAAA,MAAA,SAAA,oDAAA,aAAa,KAAK;AAChC,kBAAU,QAAQ;AAClB,kBAAU,QAAQ;AAElB,YAAI,aAAa,OAAO;AACtBA,wBAAG,MAAC,oBAAmB;AACvB,uBAAa,QAAQ;AAAA,QACtB;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,eAAe,YAAY;AAC/B,UAAI,gBAAgB,OAAO;AAEzB,cAAM,eAAe,cAAc,MAAM,KAAM,CAAA;AAAA,MACnD,OAAS;AAEL,cAAM,qBAAoB;AAAA,MAC3B;AAAA,IACH;AAGA,UAAM,uBAAuB,YAAY;AACvC,YAAM,OAAO,SAAS,KAAK,KAAI;AAC/B,YAAM,SAAS,SAAS,KAAK,KAAI;AAEjC,UAAI,CAAC,QAAQ,CAAC,QAAQ;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AAEDA,oBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,YAAM,SAAS,MAAME,cAAI,eAAe,uBAAuB;AAAA,QAC7D;AAAA,QACA,SAAS;AAAA,MACb,CAAG;AAEDF,oBAAG,MAAC,YAAW;AAEfA,oBAAA,MAAA,MAAA,OAAA,oDAAY,OAAO,MAAK,YAAW,MAAM;AACzC,UAAI,OAAO,SAAS,WAAW;AAC7B,cAAM,QAAQ,OAAO;AACrB,sBAAc,QAAQ;AAAA,MAC1B,OAAS;AACL,sBAAc,QAAQ;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,OAAO;AAAA,UACd,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,YAAM,OAAO,SAAS,KAAK,KAAI;AAC/B,YAAM,SAAS,SAAS,KAAK,KAAI;AAEjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yBAAyB,mBAAmB,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC;AAAA,QAC3F,SAAS,MAAM;AACbA,wBAAAA,uEAAY,SAAS;AAAA,QACtB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,oDAAc,QAAQ,GAAG;AACzBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,UAAI,gBAAgB,OAAO;AACzB;MACD;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,aAAa;AAClC,YAAM,YAAY;AAAA,QAChB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACX;AACE,aAAO,UAAU,QAAQ,KAAK;AAAA,IAChC;AAGA,UAAM,aAAa,CAAC,YAAY;AAC9B,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,aAAO,GAAG,KAAK,YAAW,CAAE,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACzH;AAGA,UAAM,mBAAmB,CAAC,SAAS,gBAAgB;AACjDA,2FAAY,SAAS,oBAAoB,WAAW;AAEpD,UAAI,gBAAgB,OAAO;AACzBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,iDAAiD,OAAO,WAAW,WAAW;AAAA,QACzF,CAAK;AAAA,MACL,WAAa,gBAAgB,OAAO;AAChCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,oDAAoD,OAAO,WAAW,WAAW;AAAA,QAC5F,CAAK;AAAA,MACF;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/WA,GAAG,WAAW,eAAe;"}