// 统一API管理模块 - 集成路径定义和调用方法
// 引入HTTP请求拦截器
import { request, authUtils } from '@/server/require.js';

// ==================== API路径常量定义 ====================

/**
 * 用户相关API路径
 */
export const USER_API = {
  // 基础用户操作
  INFO: '/user/user_info/',
  LOGIN: '/user/login',
  UPDATE: '/user/update',
  BIND_PHONE: '/user/bind-phone',
  SEND_CODE: '/user/send-code',
  PHONE_LOGIN: '/user/phone-login',

  // 用户操作日志
  OPERATION_LOG: '/user/operation_log/'
};

/**
 * 微信认证相关API路径
 */
export const WECHAT_API = {
  LOGIN: '/user/wechat/login/',
  REFRESH: '/user/wechat/refresh/',
  BIND: '/wechat/bind/',
  UNBIND: '/wechat/unbind/',
  USER_INFO: '/wechat/userinfo/',
  FACEID_AUTH: '/wechat/faceid/auth/'  // 人脸核身认证接口
};

/**
 * 首页相关API路径
 */
export const HOME_API = {
  GRID_DATA: '/home/<USER>'
};

/**
 * 调解查询相关API路径
 */
export const MEDIATION_QUERY_API = {
  LIST: '/mediation_management/mediation_case/wechat/list/',
  DETAIL: '/mediation-query/detail',
  CASE_BY_NUMBER: '/mediation_management/mediation_case/',
  CASE_COUNT_BY_IDENTITY: '/mediation_management/mediation_case/wechat/by_debtor/'
};

/**
 * 工单相关API路径
 */
export const WORK_ORDER_API = {
  DETAIL: '/work-order/detail',
  ACCEPT: '/work-order/accept',
  REJECT: '/work-order/reject'
};

/**
 * 调解方案相关API路径
 */
export const SOLUTION_API = {
  DETAIL: '/solution/detail',
  CONFIRM: '/solution/confirm',
  ADJUST: '/solution/adjust'
};

/**
 * 债权确认相关API路径
 */
export const DEBT_CONFIRM_API = {
  LIST: '/debt-confirm/list',
  DETAIL: '/debt-confirm/detail',
  SUBMIT: '/debt-confirm/submit'
};

/**
 * 调解投诉相关API路径
 */
export const MEDIATION_COMPLAINT_API = {
  LIST: '/mediation-complaint/list',
  DETAIL: '/mediation-complaint/detail',
  SUBMIT: '/mediation-complaint/submit'
};

/**
 * 案例展示相关API路径
 */
export const REAL_CASE_API = {
  LIST: '/case_display/case_display/',
  DETAIL: '/real-case/detail'
};

/**
 * 意见反馈相关API路径
 */
export const FEEDBACK_API = {
  SUBMIT: '/feedback/submit',
  HISTORY: '/feedback/history'
};

/**
 * 所有API路径的统一导出
 */
export const API_PATHS = {
  USER: USER_API,
  WECHAT: WECHAT_API,
  HOME: HOME_API,
  MEDIATION_QUERY: MEDIATION_QUERY_API,
  WORK_ORDER: WORK_ORDER_API,
  SOLUTION: SOLUTION_API,
  DEBT_CONFIRM: DEBT_CONFIRM_API,
  MEDIATION_COMPLAINT: MEDIATION_COMPLAINT_API,
  REAL_CASE: REAL_CASE_API,
  FEEDBACK: FEEDBACK_API
};

/**
 * 根据路径模板和参数生成完整URL
 * @param {string} pathTemplate - 路径模板（如 '/user/detail/{id}'）
 * @param {object} params - 参数对象
 * @returns {string} 完整的URL路径
 */
export const buildApiPath = (pathTemplate, params = {}) => {
  let path = pathTemplate;

  // 替换路径中的参数占位符
  Object.keys(params).forEach(key => {
    path = path.replace(`{${key}}`, params[key]);
  });

  return path;
};

/**
 * 获取带参数的API路径辅助函数
 */
export const getApiPath = {
  // 获取用户详情路径
  userDetail: (id) => `${USER_API.DETAIL}/${id}`,

  // 获取调解查询详情路径
  mediationQueryDetail: (id) => `${MEDIATION_QUERY_API.DETAIL}/${id}`,

  // 获取工单详情路径
  workOrderDetail: (id) => `${WORK_ORDER_API.DETAIL}/${id}`,

  // 获取工单接受路径
  workOrderAccept: (id) => `${WORK_ORDER_API.ACCEPT}/${id}`,

  // 获取工单拒绝路径
  workOrderReject: (id) => `${WORK_ORDER_API.REJECT}/${id}`,

  // 获取调解方案详情路径
  solutionDetail: (orderId) => `${SOLUTION_API.DETAIL}/${orderId}`,

  // 获取调解方案确认路径
  solutionConfirm: (orderId) => `${SOLUTION_API.CONFIRM}/${orderId}`,

  // 获取调解方案调整路径
  solutionAdjust: (orderId) => `${SOLUTION_API.ADJUST}/${orderId}`,

  // 获取债权确认详情路径
  debtConfirmDetail: (id) => `${DEBT_CONFIRM_API.DETAIL}/${id}`,

  // 获取调解投诉详情路径
  mediationComplaintDetail: (id) => `${MEDIATION_COMPLAINT_API.DETAIL}/${id}`,

  // 获取案例详情路径
  realCaseDetail: (id) => `${REAL_CASE_API.DETAIL}/${id}`
};

// ==================== API调用方法 ====================

// API调用模块 - 保持原有结构，确保向后兼容
export const api = {
  // Token管理工具 - 使用server/require.js中的authUtils
  auth: authUtils,
  
  // 用户相关
  user: {
    // 获取用户信息
    getUserInfo: () => {
      return request({
        url: USER_API.INFO,
        method: 'GET'
      });
    },

    // 普通登录
    login: (data) => {
      return request({
        url: USER_API.LOGIN,
        method: 'POST',
        data
      });
    },

    // 更新用户信息
    updateUserInfo: (data) => {
      return request({
        url: USER_API.UPDATE,
        method: 'PUT',
        data
      });
    },

    // 绑定手机号
    bindPhone: (data) => {
      return request({
        url: USER_API.BIND_PHONE,
        method: 'POST',
        data
      });
    },

    // 发送验证码
    sendVerifyCode: (data) => {
      return request({
        url: USER_API.SEND_CODE,
        method: 'POST',
        data
      });
    },

    // 手机号登录
    phoneLogin: (data) => {
      return request({
        url: USER_API.PHONE_LOGIN,
        method: 'POST',
        data
      });
    }
  },

  // 微信认证相关
  wechat: {
    // 微信登录
    login: (data) => {
      return request({
        url: WECHAT_API.LOGIN,
        method: 'POST',
        data
      });
    },

    // 微信token刷新
    refresh: (data) => {
      return request({
        url: WECHAT_API.REFRESH,
        method: 'POST',
        data
      });
    },

    // 绑定微信
    bind: (data) => {
      return request({
        url: WECHAT_API.BIND,
        method: 'POST',
        data
      });
    },

    // 解绑微信
    unbind: () => {
      return request({
        url: WECHAT_API.UNBIND,
        method: 'POST'
      });
    },

    // 获取微信用户信息
    getUserInfo: (data) => {
      return request({
        url: WECHAT_API.USER_INFO,
        method: 'POST',
        data
      });
    },

    // 人脸核身认证
    faceIdAuth: (data) => {
      return request({
        url: WECHAT_API.FACEID_AUTH,
        method: 'POST',
        data
      });
    }
  },
  
  // 首页相关
  home: {
    // 获取首页卡片数据
    getGridData: () => {
      return request({
        url: HOME_API.GRID_DATA,
        method: 'GET'
      });
    }
  },
  
  // 调解查询
  mediationQuery: {
    // 获取调解查询列表
    getAuthenticatedList: (params) => {
      return request({
        url: MEDIATION_QUERY_API.LIST,
        method: 'GET',
        data: params
      });
    },

    // 获取调解查询详情
    getDetail: (id) => {
      return request({
        url: getApiPath.mediationQueryDetail(id),
        method: 'GET'
      });
    },

    // 根据调解案件号查询调解案件
    getCaseByNumber: (caseNumber) => {
      return request({
        url: MEDIATION_QUERY_API.CASE_BY_NUMBER,
        method: 'GET',
        data: {
          case_number: caseNumber
        }
      });
    },

    // 根据身份信息查询调解案件数量
    getCaseCountByIdentity: (params) => {
      return request({
        url: MEDIATION_QUERY_API.CASE_COUNT_BY_IDENTITY,
        method: 'GET',
        data: params
      });
    }
  },
  
  // 工单相关
  workOrder: {
    // 获取工单详情
    getDetail: (id) => {
      return request({
        url: getApiPath.workOrderDetail(id),
        method: 'GET'
      });
    },
    
    // 接受工单
    acceptWorkOrder: (id) => {
      return request({
        url: getApiPath.workOrderAccept(id),
        method: 'POST'
      });
    },
    
    // 拒绝工单
    rejectWorkOrder: (id, reason) => {
      return request({
        url: getApiPath.workOrderReject(id),
        method: 'POST',
        data: { reason }
      });
    }
  },
  
  // 调解方案相关
  solution: {
    // 获取调解方案详情
    getDetail: (orderId) => {
      return request({
        url: getApiPath.solutionDetail(orderId),
        method: 'GET'
      });
    },
    
    // 确认调解方案
    confirmSolution: (orderId) => {
      return request({
        url: getApiPath.solutionConfirm(orderId),
        method: 'POST'
      });
    },
    
    // 申请调整方案
    adjustSolution: (orderId, data) => {
      return request({
        url: getApiPath.solutionAdjust(orderId),
        method: 'POST',
        data
      });
    }
  },
  
  // 债权确认
  debtConfirm: {
    // 获取债权确认列表
    getList: (params) => {
      return request({
        url: DEBT_CONFIRM_API.LIST,
        method: 'GET',
        data: params
      });
    },
    
    // 获取债权确认详情
    getDetail: (id) => {
      return request({
        url: getApiPath.debtConfirmDetail(id),
        method: 'GET'
      });
    },
    
    // 提交债权确认
    submit: (data) => {
      return request({
        url: DEBT_CONFIRM_API.SUBMIT,
        method: 'POST',
        data
      });
    }
  },
  
  // 调解投诉
  mediationComplaint: {
    // 获取投诉列表
    getList: (params) => {
      return request({
        url: MEDIATION_COMPLAINT_API.LIST,
        method: 'GET',
        data: params
      });
    },
    
    // 获取投诉详情
    getDetail: (id) => {
      return request({
        url: getApiPath.mediationComplaintDetail(id),
        method: 'GET'
      });
    },
    
    // 提交投诉
    submit: (data) => {
      return request({
        url: MEDIATION_COMPLAINT_API.SUBMIT,
        method: 'POST',
        data
      });
    }
  },
  
  // 案例展示
  realCase: {
    // 获取案例列表
    getList: (params) => {
      return request({
        url: REAL_CASE_API.LIST,
        method: 'GET',
        data: params
      });
    },
    
    // 获取案例详情
    getDetail: (id) => {
      return request({
        url: getApiPath.realCaseDetail(id),
        method: 'GET'
      });
    }
  },

  // 用户操作日志 - 支持操作记录功能
  operationLog: {
    // 记录用户操作
    recordOperation: (data) => {
      return request({
        url: USER_API.OPERATION_LOG,
        method: 'POST',
        data
      });
    }
  },
  
  // 意见反馈
  feedback: {
    // 提交反馈
    submitFeedback: (data) => {
      return request({
        url: FEEDBACK_API.SUBMIT,
        method: 'POST',
        data
      });
    },
    
    // 获取反馈历史
    getFeedbackHistory: () => {
      return request({
        url: FEEDBACK_API.HISTORY,
        method: 'GET'
      });
    }
  }
};

export default api; 