
.container.data-v-98e148d1 {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  min-height: 100vh;
  padding: 20px;
}
.header.data-v-98e148d1 {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}
.title.data-v-98e148d1 {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
}
.notification.data-v-98e148d1 {
  text-align: center;
  margin-top: 20rpx;
  color: #666666;
  font-size: 14px;
}
.card-icon.data-v-98e148d1 {
  display: flex;
  justify-content: center;
  margin: 40rpx 0;
}
.icon.data-v-98e148d1 {
  width: 120rpx;
  height: 120rpx;
  background-color: #2979FF;
  border-radius: 16rpx;
}
.form-item.data-v-98e148d1 {
  margin-bottom: 30rpx;
}
.label.data-v-98e148d1 {
  font-size: 16px;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}
.input-value.data-v-98e148d1 {
  font-size: 16px;
  color: #333333;
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 15rpx;
  width: 100%;
}
.input-error.data-v-98e148d1 {
  border-bottom-color: #ff4757 !important;
}
.error-text.data-v-98e148d1 {
  color: #ff4757;
  font-size: 12px;
  margin-top: 5rpx;
  display: block;
}
.footer.data-v-98e148d1 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
}
.agreement.data-v-98e148d1 {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 14px;
  color: #666666;
}
.link.data-v-98e148d1 {
  color: #2979FF;
  margin: 0 5rpx;
}
.action-btn.data-v-98e148d1 {
  background-color: #2979FF;
  color: #ffffff;
  border-radius: 50rpx;
  font-size: 16px;
  transition: all 0.3s ease;
}
.disabled.data-v-98e148d1 {
  background-color: #CCCCCC !important;
  opacity: 0.7;
}
