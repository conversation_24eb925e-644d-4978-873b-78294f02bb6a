<view class="container data-v-98e148d1"><view class="header data-v-98e148d1"><text class="title data-v-98e148d1">人脸识别认证</text></view><view class="notification data-v-98e148d1"><text class="data-v-98e148d1">当前业务需要人脸识别验证</text></view><view class="card-icon data-v-98e148d1"><image src="{{a}}" class="icon data-v-98e148d1"/></view><view class="form-item data-v-98e148d1"><text class="label data-v-98e148d1">姓名</text><input class="{{['input-value', 'data-v-98e148d1', b && 'input-error']}}" placeholder="请输入姓名" bindinput="{{c}}" bindblur="{{d}}" value="{{e}}"/><text wx:if="{{f}}" class="error-text data-v-98e148d1">{{g}}</text></view><view class="form-item data-v-98e148d1"><text class="label data-v-98e148d1">身份证号码</text><input class="{{['input-value', 'data-v-98e148d1', h && 'input-error']}}" placeholder="请输入身份证号码" bindinput="{{i}}" bindblur="{{j}}" maxlength="18" value="{{k}}"/><text wx:if="{{l}}" class="error-text data-v-98e148d1">{{m}}</text></view><view class="footer data-v-98e148d1"><view class="agreement data-v-98e148d1"><checkbox-group class="data-v-98e148d1" bindchange="{{o}}"><checkbox class="data-v-98e148d1" checked="{{n}}" color="#2979FF"/></checkbox-group><text class="data-v-98e148d1">我已阅读并同意遵循</text><text class="link data-v-98e148d1" bindtap="{{p}}">《用户服务协议》</text><text class="data-v-98e148d1">和</text><text class="link data-v-98e148d1" bindtap="{{q}}">《个人信息保护政策》</text></view><button disabled="{{s}}" class="{{['action-btn', 'data-v-98e148d1', t && 'disabled']}}" bindtap="{{v}}">{{r}}</button></view></view>